# **User Onboarding Program**

## **Progressive Onboarding Flow**

### **Day 1: Welcome & First Culture**
```mermaid
graph LR
    A[Welcome Screen] --> B[Account Setup]
    B --> C[Choose Experience Level]
    C --> D[First Culture Tutorial]
    D --> E[Success Celebration]
    E --> F[Next Steps Guide]
```

**Implementation:**
```kotlin
data class OnboardingFlow(
    val steps: List<OnboardingStep>,
    val currentStepIndex: Int = 0,
    val completedSteps: Set<String> = emptySet(),
    val userProfile: UserProfile?
) {
    val currentStep: OnboardingStep? get() = steps.getOrNull(currentStepIndex)
    val isComplete: Boolean get() = currentStepIndex >= steps.size
    val progressPercentage: Float get() = currentStepIndex.toFloat() / steps.size
}

sealed class OnboardingStep(
    val id: String,
    val title: String,
    val description: String,
    val isSkippable: Boolean = false
) {

    object Welcome : OnboardingStep(
        id = "welcome",
        title = "Welcome to CultureStack",
        description = "Your journey to successful tissue culture starts here"
    )

    object ExperienceLevel : OnboardingStep(
        id = "experience",
        title = "Tell us about your experience",
        description = "This helps us provide the right guidance for you"
    )

    object FirstCulture : OnboardingStep(
        id = "first_culture",
        title = "Create your first culture",
        description = "Let's set up your first tissue culture project together"
    )

    object NotificationSetup : OnboardingStep(
        id = "notifications",
        title = "Stay on track with reminders",
        description = "Enable notifications to never miss important culture care tasks",
        isSkippable = true
    )
}
```

### **Week 1: Observation & Monitoring**
- **Day 3:** First observation tutorial
- **Day 5:** Photo documentation guide
- **Day 7:** Progress tracking explanation

### **Week 2-3: Advanced Features**
- **Day 10:** Recipe management introduction
- **Day 14:** Subculturing preparation
- **Day 21:** Success rate analysis

## **Onboarding Content Library**

### **Interactive Tutorials**

**Tutorial 1: Creating Your First Culture**
```yaml
id: "first-culture-tutorial"
title: "Creating Your First Culture"
estimated_duration: "5-7 minutes"
difficulty: "beginner"
prerequisites: []

steps:
  - id: "species-selection"
    title: "Choose Your Plant Species"
    instruction: "Select the plant you want to propagate. Start with something familiar to you."
    target_element: "species_input"
    validation:
      type: "text_not_empty"
      message: "Please enter a species name"

  - id: "explant-type"
    title: "Select Explant Type"
    instruction: "Choose the part of the plant you'll be using. Leaf segments are great for beginners."
    target_element: "explant_selector"
    help_content: "Explants are the plant parts used to start tissue culture. Different parts have different success rates."

  - id: "medium-selection"
    title: "Choose Growing Medium"
    instruction: "Select a pre-made recipe or create your own. MS medium is a good starting point."
    target_element: "recipe_selector"
    help_content: "Growing medium provides nutrients for your culture. Pre-made recipes are tested and reliable."

completion_reward:
  type: "achievement"
  title: "Culture Creator"
  description: "You've successfully created your first culture!"
```

**Tutorial 2: Recording Observations**
```yaml
id: "observation-tutorial"
title: "Recording Your First Observation"
estimated_duration: "3-4 minutes"
difficulty: "beginner"
prerequisites: ["first-culture-tutorial"]

steps:
  - id: "observation-access"
    title: "Access Observation Screen"
    instruction: "Tap on your culture, then tap 'Add Observation'"
    target_element: "add_observation_button"

  - id: "contamination-check"
    title: "Check for Contamination"
    instruction: "Look for any unusual colors, fuzzy growth, or bad smells"
    help_content: "Contamination appears as bacterial (cloudy), fungal (fuzzy), or yeast (foamy) growth"
    media_url: "contamination_examples_video.mp4"

  - id: "photo-capture"
    title: "Take Progress Photos"
    instruction: "Take clear photos to track growth over time"
    target_element: "photo_capture_button"
    validation:
      type: "photo_taken"
      message: "Taking photos helps track progress and identify issues"
```

---
